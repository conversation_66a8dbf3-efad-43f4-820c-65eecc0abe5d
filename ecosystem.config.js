module.exports = {
  apps: [
    {
      name: 'monoapi',
      script: 'dist/main.js',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      // PM2 cluster mode configuration
      listen_timeout: 10000,
      kill_timeout: 5000,

      // Logging configuration
      log_file: '/var/log/pm2/monoapi.log',
      out_file: '/var/log/pm2/monoapi-out.log',
      error_file: '/var/log/pm2/monoapi-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',

      // Memory and CPU monitoring
      max_memory_restart: '1G',

      // Restart configuration
      autorestart: false,
      watch: false,
      max_restarts: 10,
      min_uptime: '10s',

      // Health check
      health_check_grace_period: 3000,
    },
  ],
};
