FROM node:18.15-alpine3.17 as builder
WORKDIR /usr/src/app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build
RUN npm pkg delete scripts.prepare
RUN npm ci --only=production

FROM node:18.15-alpine3.17
RUN npm install pm2 -g
WORKDIR /usr/src/app
COPY --from=builder /usr/src/app .
COPY ecosystem.config.js .
RUN mkdir -p /var/log/pm2
EXPOSE 3000

# Use pm2-runtime which is designed for Docker containers
# It runs in foreground mode and properly handles signals
# Environment variables are automatically inherited by pm2-runtime
CMD ["pm2-runtime", "start", "ecosystem.config.js", "--env", "production"]