import { Injectable, Logger } from '@nestjs/common';
import { TransactionToUpdate } from '../../dtos/update-delivery-word-by-batch';
import { TransactionRepository } from 'src/infrastructure/database/repositories/transaction.repository';
import { TransactionStatus } from '../../../constants';
import { Transaction } from 'src/infrastructure/database/models/transaction.entity';

@Injectable()
export class UpdateDeliveryWordByBatchService {
  logger = new Logger(UpdateDeliveryWordByBatchService.name);
  constructor(private readonly transactionRepository: TransactionRepository) {}

  private async handleTransactionUpdate(
    transaction: Transaction,
    transactionOnBatch: TransactionToUpdate,
  ) {
    if (transaction.status.name !== TransactionStatus.ON_HOLD) {
      return this.logger.error(
        `Transaction ${transaction.id} is not available to update the delivery word because is in status: ${transaction.status.name}`,
      );
    }

    if (!transactionOnBatch) {
      return this.logger.error(
        `Transaction ${transaction.id} is not available to update the delivery word`,
      );
    }

    await this.transactionRepository.updateDeliveryMetadata({
      transactionId: transaction.id,
      deliveryDate: transactionOnBatch.deliveryDate,
      metadata: {
        ...transaction.metadata,
        deliveryWord: transactionOnBatch.deliveryWord,
      },
    });

    return transaction;
  }

  async handler({
    transactionsToUpdate,
    wholesalerid,
  }: {
    transactionsToUpdate: TransactionToUpdate[];
    wholesalerid: string;
  }) {
    const transactionIdsToUpdate = transactionsToUpdate.map(
      (transaction) => transaction.transactionId,
    );
    const updatedTransactions = [];

    const transactions = await this.transactionRepository.findTransactionsByBatch(
      transactionIdsToUpdate,
      wholesalerid,
    );

    for (const transaction of transactions) {
      try {
        const transactionOnBatch = transactionsToUpdate.find(
          ({ transactionId }) => transactionId === transaction.id,
        );

        const updatedTransaction = await this.handleTransactionUpdate(
          transaction,
          transactionOnBatch,
        );

        if (updatedTransaction) {
          updatedTransactions.push(transactionOnBatch);
        }
      } catch (error) {
        this.logger.error(`Error updating delivery word for transaction ${transaction.id}`, error);
      }
    }

    return { updatedTransactions };
  }
}
