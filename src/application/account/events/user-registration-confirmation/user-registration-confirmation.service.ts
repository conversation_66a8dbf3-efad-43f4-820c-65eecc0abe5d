import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { Environments } from 'src/application/constants';
import { Users } from 'src/infrastructure/database/models/users.entity';
import { WholesalerStrategyService } from 'src/infrastructure/wholesaler/wholesaler-strategy.service';

@Injectable()
export class UserRegistrationConfirmationService {
  private logger = new Logger(UserRegistrationConfirmationService.name);
  constructor(private readonly wholesalerStrategyService: WholesalerStrategyService) {}

  @OnEvent('user.register.confirmation')
  async handleUserRegisteredEvent({
    user,
    confirmationId,
    status,
    wholesalerName,
  }: {
    user: Users;
    confirmationId: string;
    status: string;
    wholesalerName: string;
  }) {
    if (
      process.env.NODE_ENV !== Environments.PRODUCTION &&
      process.env.NODE_ENV !== Environments.STAGING
    ) {
      this.logger.log(
        `It should create send user registration confirmation, but you are in ${process.env.NODE_ENV} env`,
      );
      return false;
    }

    this.logger.log(`Sending confirmation for user ${user.id} registered with status ${status}`);
    return this.wholesalerStrategyService.sendUserRegistrationConfirmation({
      userId: user.id,
      confirmationId,
      status,
      wholesalerName,
    });
  }
}
